# MenUI + Async Settings Setup Checklist

## 🎯 Goal
Setup MenUI in **Ouroboros - Base.unity** to load **Async Settings.unity** as an async overlay scene for settings.

## ✅ Files Already Created

### **SceneReference System**
- ✅ `SceneReference.cs` - ScriptableObject for robust scene references
- ✅ `Scene References/Async Settings Scene.asset` - Points to Async Settings scene
- ✅ `SCENE_REFERENCE_GUIDE.md` - Complete usage guide

### **MenUI Integration**
- ✅ `SimpleMenUISystem.cs` - Updated to use SceneReference
- ✅ `LastUISceneBridge.cs` - Fixed pause state management
- ✅ `LastUIMenUIBridge.cs` - Handles exit from Last UI back to MenUI

### **Setup Tools**
- ✅ `AsyncSettingsSceneSetup.cs` - Auto-setup for Async Settings scene
- ✅ `PauseStateDebugger.cs` - Debug tool for monitoring pause state

### **Documentation**
- ✅ `OUROBOROS_ASYNC_SETTINGS_SETUP.md` - Complete setup guide
- ✅ `PAUSE_STATE_FIX.md` - Pause behavior documentation

## 📋 Manual Setup Steps Required

### **1. Configure Ouroboros - Base Scene**

1. **Open** `Assets/_Scenes/Levels/Ouroboros - Base.unity`

2. **Find MenUI Canvas GameObject**:
   - Look for existing "MenUI Canvas" in hierarchy
   - Select the GameObject with `SimpleMenUISystem` component

3. **Configure SimpleMenUISystem Inspector**:
   - **Last UI Scene Reference**: Drag `Assets/Stylo/MenUI/Scene References/Async Settings Scene.asset`
   - **Hide Pause Menu When Settings Open**: `false` (keep visible as overlay)
   - **Enable Debug Mode**: `true` (for testing)

4. **Verify GameManager Connection** (if applicable):
   - Find GameManager GameObject
   - **Pause Menu Manager**: Should point to MenUI Canvas GameObject

### **2. Setup Async Settings Scene**

1. **Open** `Assets/Settings Menu/Last UI/Scenes/Async Settings.unity`

2. **Add AsyncSettingsSceneSetup Component**:
   - Create empty GameObject named "MenUI Integration Setup"
   - Add `AsyncSettingsSceneSetup` component
   - Click "Setup Async Settings Scene" button in inspector
   - This will automatically create all required components

3. **Verify Auto-Created Components**:
   - `SceneServiceLocator` - Scene-scoped service management
   - `LastUISettingsManager` - Settings persistence
   - `SwitchHandlerAutoConfig` - Toggle setting ID configuration
   - `LastUIMenUIBridge` - Exit handling back to MenUI

### **3. Add Scene to Build Settings**

1. **Open Build Settings** (File → Build Settings)
2. **Add Async Settings scene**:
   - Click "Add Open Scenes" while Async Settings scene is open
   - OR drag scene file from Project window to build settings
3. **Verify scene is enabled** in build settings list

## 🧪 Testing Procedure

### **Test Sequence**
1. **Enter Play Mode** in Ouroboros - Base scene
2. **Press Escape** → MenUI pause menu should appear
3. **Click Settings** → Async Settings scene should load as overlay
4. **Verify game stays paused** → TimeScale should remain 0
5. **Change some settings** → Toggle VSync, adjust sliders, etc.
6. **Press Escape** → Should return to MenUI pause menu
7. **Click Settings again** → Settings should be preserved
8. **Click Resume** → Game should unpause and continue

### **Expected Console Output**
```
[SimpleMenUISystem] Settings button clicked. Current pause state: True, TimeScale: 0
[LastUISceneBridge] Loading Last UI scene: Async Settings
[AsyncSettingsSceneSetup] Setting up Async Settings scene for MenUI integration...
[LastUIMenUIBridge] Initialized in Last UI scene
[LastUISceneBridge] Last UI scene loaded successfully
[LastUIMenUIBridge] Cancel input detected
[LastUISceneBridge] Last UI exit requested, unloading scene
[LastUISceneBridge] Last UI scene unloaded successfully
```

## 🐛 Troubleshooting

### **"Scene couldn't be loaded"**
- ✅ Check Async Settings scene is in Build Settings
- ✅ Verify SceneReference asset points to correct scene
- ✅ Check scene name is exactly "Async Settings"

### **Game unpauses when entering settings**
- ✅ Verify `hidePauseMenuWhenSettingsOpen = false` in SimpleMenUISystem
- ✅ Check console for pause state debug messages
- ✅ Add `PauseStateDebugger` component for real-time monitoring

### **Settings don't persist**
- ✅ Verify `AsyncSettingsSceneSetup` ran successfully
- ✅ Check for `LastUISettingsManager` in Async Settings scene
- ✅ Look for toggle auto-configuration messages

### **Can't exit settings**
- ✅ Verify `LastUIMenUIBridge` exists in Async Settings scene
- ✅ Check Input System configuration
- ✅ Look for "Cancel input detected" debug messages

## 🎉 Success Criteria

When setup is complete, you should have:

✅ **Seamless Navigation**: Pause → Settings → Pause → Resume  
✅ **Proper Pause State**: Game stays paused throughout settings  
✅ **Settings Persistence**: All changes save and survive scene transitions  
✅ **Visual Clarity**: Pause menu visible behind settings (overlay mode)  
✅ **Debug Feedback**: Clear console messages for troubleshooting  

## 📞 Quick Help

If you encounter issues:

1. **Check this checklist** - Ensure all steps completed
2. **Review console messages** - Look for error or warning messages
3. **Use debug tools** - Add `PauseStateDebugger` for real-time monitoring
4. **Validate components** - Use `AsyncSettingsSceneSetup.ValidateSetup()`
5. **Check documentation** - Refer to detailed guides in MenUI folder

The integration should provide a professional-grade pause menu → settings → pause menu experience with proper state management!
