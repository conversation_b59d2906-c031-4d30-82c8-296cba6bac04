using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using UnityEngine.InputSystem;
using System.Collections.Generic;
using System.Linq;

namespace Stylo.MenUI
{
    /// <summary>
    /// Hierarchical settings navigation with proper menu levels.
    /// Level 1: Category selection (Graphics/Audio/Back)
    /// Level 2: Settings within selected category
    /// </summary>
    public class HierarchicalSettingsNavigation : MonoBehaviour
    {
        [Header("Category Level (Level 1)")]
        [SerializeField] private Button[] categoryButtons;
        [SerializeField] private GameObject[] categoryPanels;
        [SerializeField] private Button mainBackButton;

        [Header("Settings Level (Level 2)")]
        [SerializeField] private bool addBackButtonsToCategories = false;

        [Header("Navigation")]
        [SerializeField] private Button firstPauseMenuButton;
        [SerializeField] private bool debugLogging = false;

        [Header("Input")]
        [SerializeField] private InputActionReference cancelAction;
        [SerializeField] private InputActionReference submitAction;

        private enum NavigationLevel
        {
            CategorySelection,  // Level 1: Graphics/Audio/Back
            SettingsSelection,  // Level 2: Individual settings navigation
            SettingAdjustment   // Level 3: Adjusting individual setting values
        }

        private NavigationLevel currentLevel = NavigationLevel.CategorySelection;
        private int currentCategoryIndex = 0;
        private int currentCategorySelection = 0; // Which category button is selected
        private int currentSettingSelection = 0;  // Which setting is selected

        private List<Selectable>[] categorySelectables;
        private Button[] categoryBackButtons;

        private void Start()
        {
            InitializeHierarchy();
            SetupInputHandling();
            EnterCategorySelectionLevel();
        }

        private void OnEnable()
        {
            if (cancelAction != null)
                cancelAction.action.performed += OnCancelPressed;
            if (submitAction != null)
                submitAction.action.performed += OnSubmitPressed;
        }

        private void OnDisable()
        {
            if (cancelAction != null)
                cancelAction.action.performed -= OnCancelPressed;
            if (submitAction != null)
                submitAction.action.performed -= OnSubmitPressed;
        }

        private void InitializeHierarchy()
        {
            if (categoryButtons.Length != categoryPanels.Length)
            {
                Debug.LogError("[HierarchicalSettingsNavigation] Category buttons and panels count mismatch!");
                return;
            }

            // Initialize selectables array
            categorySelectables = new List<Selectable>[categoryPanels.Length];
            categoryBackButtons = new Button[categoryPanels.Length];

            // Set up each category
            for (int i = 0; i < categoryPanels.Length; i++)
            {
                SetupCategory(i);
            }

            // Set up category button click handlers
            for (int i = 0; i < categoryButtons.Length; i++)
            {
                int index = i; // Capture for closure
                categoryButtons[i].onClick.AddListener(() => EnterCategorySettings(index));
            }

            // Set up main back button
            if (mainBackButton != null)
                mainBackButton.onClick.AddListener(() => ExitToMainMenu());

            if (debugLogging)
                Debug.Log($"[HierarchicalSettingsNavigation] Initialized {categoryButtons.Length} categories");
        }

        private void SetupCategory(int categoryIndex)
        {
            var categoryPanel = categoryPanels[categoryIndex];

            // Find all selectables in this category
            var allSelectables = categoryPanel.GetComponentsInChildren<Selectable>(true)
                .Where(s => s.interactable && s != null)
                .OrderBy(s => -s.transform.position.y)
                .ThenBy(s => s.transform.position.x)
                .ToList();

            // Configure Settings Generator components for proper navigation
            ConfigureSettingsGeneratorComponents(categoryPanel);

            // Add back button to category if enabled
            if (addBackButtonsToCategories)
            {
                var backButton = CreateCategoryBackButton(categoryPanel, categoryIndex);
                categoryBackButtons[categoryIndex] = backButton;
                allSelectables.Insert(0, backButton); // Add at beginning
            }

            categorySelectables[categoryIndex] = allSelectables;

            // Hide category panel initially
            categoryPanel.SetActive(false);

            if (debugLogging)
                Debug.Log($"[HierarchicalSettingsNavigation] Category {categoryIndex} ({categoryPanel.name}) has {allSelectables.Count} selectables");
        }

        private void ConfigureSettingsGeneratorComponents(GameObject categoryPanel)
        {
            // Find and configure Settings Generator components for proper controller navigation
            var optionsButtons = categoryPanel.GetComponentsInChildren<OptionsButtonUGUI>(true);
            var steppers = categoryPanel.GetComponentsInChildren<StepperUGUI>(true);
            var sliders = categoryPanel.GetComponentsInChildren<SliderUGUI>(true);
            var dropdowns = categoryPanel.GetComponentsInChildren<DropDownUGUI>(true);

            // Disable button controls for Settings Generator components that have them
            foreach (var optionsButton in optionsButtons)
            {
                optionsButton.EnableButtonControls = false; // We'll handle this manually
                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] Configured OptionsButtonUGUI: {optionsButton.name}");
            }

            foreach (var stepper in steppers)
            {
                stepper.EnableButtonControls = false; // We'll handle this manually
                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] Configured StepperUGUI: {stepper.name}");
            }

            foreach (var slider in sliders)
            {
                slider.UseMoveCommandToChangeValue = false; // We'll handle this manually
                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] Configured SliderUGUI: {slider.name}");
            }

            // DropDownUGUI doesn't have EnableButtonControls - it uses standard TMP_Dropdown behavior
            foreach (var dropdown in dropdowns)
            {
                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] Found DropDownUGUI: {dropdown.name} (uses standard dropdown navigation)");
            }
        }

        private Button CreateCategoryBackButton(GameObject categoryPanel, int categoryIndex)
        {
            // Create back button GameObject
            var backButtonGO = new GameObject($"Back Button ({categoryPanel.name})");
            backButtonGO.transform.SetParent(categoryPanel.transform, false);

            // Add RectTransform
            var rectTransform = backButtonGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 1);
            rectTransform.anchorMax = new Vector2(0, 1);
            rectTransform.anchoredPosition = new Vector2(100, -30);
            rectTransform.sizeDelta = new Vector2(160, 40);

            // Add Image
            var image = backButtonGO.AddComponent<Image>();
            image.color = new Color(0.2f, 0.2f, 0.2f, 1f);

            // Add Button
            var button = backButtonGO.AddComponent<Button>();
            button.targetGraphic = image;
            button.onClick.AddListener(() => ExitCategorySettings());

            // Add Text
            var textGO = new GameObject("Text");
            textGO.transform.SetParent(backButtonGO.transform, false);
            var textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;

            var text = textGO.AddComponent<Text>();
            text.text = "Back";
            text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            text.fontSize = 14;
            text.color = Color.white;
            text.alignment = TextAnchor.MiddleCenter;

            return button;
        }

        private void SetupInputHandling()
        {
            // Input handling is done through the cancel action and button navigation
        }

        private void OnCancelPressed(InputAction.CallbackContext context)
        {
            if (currentLevel == NavigationLevel.SettingAdjustment)
            {
                ExitSettingAdjustment();
            }
            else if (currentLevel == NavigationLevel.SettingsSelection)
            {
                ExitCategorySettings();
            }
            else if (currentLevel == NavigationLevel.CategorySelection)
            {
                ExitToMainMenu();
            }
        }

        private void OnSubmitPressed(InputAction.CallbackContext context)
        {
            if (currentLevel == NavigationLevel.CategorySelection)
            {
                // Submit on category level enters the category
                var selectedButton = EventSystem.current?.currentSelectedGameObject?.GetComponent<Button>();
                if (selectedButton != null)
                {
                    // Find which category button this is
                    for (int i = 0; i < categoryButtons.Length; i++)
                    {
                        if (categoryButtons[i] == selectedButton)
                        {
                            EnterCategorySettings(i);
                            return;
                        }
                    }

                    // Check if it's the main back button
                    if (selectedButton == mainBackButton)
                    {
                        ExitToMainMenu();
                    }
                }
            }
            else if (currentLevel == NavigationLevel.SettingsSelection)
            {
                // Submit on settings level enters setting adjustment mode
                EnterSettingAdjustment();
            }
        }

        #region Level 1: Category Selection

        public void EnterCategorySelectionLevel()
        {
            currentLevel = NavigationLevel.CategorySelection;

            // Hide all category panels
            for (int i = 0; i < categoryPanels.Length; i++)
            {
                categoryPanels[i].SetActive(false);
            }

            // Setup navigation for category buttons only
            SetupCategoryButtonNavigation();

            // Focus first category button
            if (categoryButtons.Length > 0)
            {
                categoryButtons[currentCategorySelection].Select();
            }

            if (debugLogging)
                Debug.Log("[HierarchicalSettingsNavigation] Entered Category Selection Level");
        }

        private void SetupCategoryButtonNavigation()
        {
            // Create list of all category-level selectables (category buttons + main back button)
            var categoryLevelSelectables = new List<Selectable>(categoryButtons);
            if (mainBackButton != null)
                categoryLevelSelectables.Add(mainBackButton);

            // Set up restricted vertical navigation
            for (int i = 0; i < categoryLevelSelectables.Count; i++)
            {
                var nav = categoryLevelSelectables[i].navigation;
                nav.mode = Navigation.Mode.Explicit;

                // Vertical navigation only
                nav.selectOnUp = (i > 0) ? categoryLevelSelectables[i - 1] : categoryLevelSelectables[categoryLevelSelectables.Count - 1];
                nav.selectOnDown = (i < categoryLevelSelectables.Count - 1) ? categoryLevelSelectables[i + 1] : categoryLevelSelectables[0];

                // No horizontal navigation
                nav.selectOnLeft = null;
                nav.selectOnRight = null;

                categoryLevelSelectables[i].navigation = nav;
            }
        }

        public void EnterCategorySettings(int categoryIndex)
        {
            if (categoryIndex < 0 || categoryIndex >= categoryPanels.Length)
            {
                if (debugLogging)
                    Debug.LogError($"[HierarchicalSettingsNavigation] Invalid category index: {categoryIndex}");
                return;
            }

            if (categoryPanels[categoryIndex] == null)
            {
                if (debugLogging)
                    Debug.LogError($"[HierarchicalSettingsNavigation] Category panel {categoryIndex} is null!");
                return;
            }

            currentLevel = NavigationLevel.SettingsSelection;
            currentCategoryIndex = categoryIndex;
            currentSettingSelection = 0;

            if (debugLogging)
                Debug.Log($"[HierarchicalSettingsNavigation] Entering category {categoryIndex} ({categoryPanels[categoryIndex].name})");

            // Show only the selected category panel
            for (int i = 0; i < categoryPanels.Length; i++)
            {
                bool shouldBeActive = (i == categoryIndex);
                categoryPanels[i].SetActive(shouldBeActive);

                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] Panel {i} ({categoryPanels[i].name}) set to active: {shouldBeActive}");
            }

            // Refresh selectables for this category (in case they weren't found during initialization)
            RefreshCategorySelectables(categoryIndex);

            // Setup navigation for settings in this category
            SetupSettingsNavigation(categoryIndex);

            // Focus first setting
            var selectables = categorySelectables[categoryIndex];
            if (selectables.Count > 0)
            {
                StartCoroutine(DelayedFocusFirstSetting(categoryIndex));
            }
            else
            {
                if (debugLogging)
                    Debug.LogWarning($"[HierarchicalSettingsNavigation] No selectables found in category {categoryIndex}");
            }

            if (debugLogging)
                Debug.Log($"[HierarchicalSettingsNavigation] Entered Category Settings Level: {categoryPanels[categoryIndex].name}");
        }

        private System.Collections.IEnumerator DelayedFocusFirstSetting(int categoryIndex)
        {
            // Wait a frame for UI to update
            yield return null;

            var selectables = categorySelectables[categoryIndex];
            if (selectables.Count > 0)
            {
                selectables[0].Select();
                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] Focused first setting: {selectables[0].name}");
            }
        }

        private void RefreshCategorySelectables(int categoryIndex)
        {
            if (categoryIndex < 0 || categoryIndex >= categoryPanels.Length) return;

            var categoryPanel = categoryPanels[categoryIndex];
            if (categoryPanel == null) return;

            // Find all selectables in this category (excluding back button we might add)
            var allSelectables = categoryPanel.GetComponentsInChildren<Selectable>(true)
                .Where(s => s.interactable && s != null && !s.name.Contains("Back Button"))
                .OrderBy(s => -s.transform.position.y)
                .ThenBy(s => s.transform.position.x)
                .ToList();

            // Add back button if it exists
            if (categoryBackButtons[categoryIndex] != null)
            {
                allSelectables.Insert(0, categoryBackButtons[categoryIndex]);
            }

            categorySelectables[categoryIndex] = allSelectables;

            if (debugLogging)
            {
                Debug.Log($"[HierarchicalSettingsNavigation] Refreshed category {categoryIndex}, found {allSelectables.Count} selectables:");
                foreach (var selectable in allSelectables)
                {
                    Debug.Log($"  - {selectable.name} ({selectable.GetType().Name})");
                }
            }
        }

        #endregion

        #region Level 2: Settings Selection

        private void SetupSettingsNavigation(int categoryIndex)
        {
            var selectables = categorySelectables[categoryIndex];
            if (selectables.Count == 0) return;

            // Set up restricted vertical navigation within settings
            for (int i = 0; i < selectables.Count; i++)
            {
                var nav = selectables[i].navigation;
                nav.mode = Navigation.Mode.Explicit;

                // Vertical navigation only (when not in adjustment mode)
                nav.selectOnUp = (i > 0) ? selectables[i - 1] : selectables[selectables.Count - 1];
                nav.selectOnDown = (i < selectables.Count - 1) ? selectables[i + 1] : selectables[0];

                // No horizontal navigation at settings level - this will be enabled in adjustment mode
                nav.selectOnLeft = null;
                nav.selectOnRight = null;

                selectables[i].navigation = nav;
            }
        }

        private void SetupSettingAdjustmentNavigation()
        {
            // In adjustment mode, we don't need to change navigation for dropdowns
            // They handle their own popup behavior when clicked
            var currentSelectable = EventSystem.current?.currentSelectedGameObject?.GetComponent<Selectable>();
            if (currentSelectable != null)
            {
                // Check if this is a dropdown - if so, don't modify navigation
                var dropdown = currentSelectable.GetComponent<TMPro.TMP_Dropdown>();
                var dropdownUGUI = currentSelectable.GetComponent<DropDownUGUI>();

                if (dropdown != null || dropdownUGUI != null)
                {
                    if (debugLogging)
                        Debug.Log($"[HierarchicalSettingsNavigation] Dropdown detected - using standard behavior for {currentSelectable.name}");
                    return;
                }

                // For other components (sliders, steppers, etc.), disable vertical navigation
                var nav = currentSelectable.navigation;
                nav.mode = Navigation.Mode.Explicit;

                // Disable vertical navigation in adjustment mode
                nav.selectOnUp = null;
                nav.selectOnDown = null;

                currentSelectable.navigation = nav;

                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] Setup adjustment navigation for {currentSelectable.name}");
            }
        }

        public void ExitCategorySettings()
        {
            if (currentLevel != NavigationLevel.SettingsSelection) return;

            // Remember which category we were in
            currentCategorySelection = currentCategoryIndex;

            // Return to category selection level
            EnterCategorySelectionLevel();

            // Focus the category button we came from
            if (currentCategorySelection < categoryButtons.Length)
            {
                categoryButtons[currentCategorySelection].Select();
            }

            if (debugLogging)
                Debug.Log("[HierarchicalSettingsNavigation] Exited to Category Selection Level");
        }

        #endregion

        #region Level 3: Setting Adjustment

        public void EnterSettingAdjustment()
        {
            if (currentLevel != NavigationLevel.SettingsSelection) return;

            currentLevel = NavigationLevel.SettingAdjustment;

            // Enable Settings Generator component controls for the current setting
            EnableCurrentSettingControls(true);

            // Setup navigation for the currently selected setting
            SetupSettingAdjustmentNavigation();

            if (debugLogging)
                Debug.Log("[HierarchicalSettingsNavigation] Entered Setting Adjustment Level");
        }

        public void ExitSettingAdjustment()
        {
            if (currentLevel != NavigationLevel.SettingAdjustment) return;

            currentLevel = NavigationLevel.SettingsSelection;

            // Disable Settings Generator component controls
            EnableCurrentSettingControls(false);

            // Restore settings navigation
            SetupSettingsNavigation(currentCategoryIndex);

            // Keep focus on the same setting
            var currentSelectable = EventSystem.current?.currentSelectedGameObject?.GetComponent<Selectable>();
            if (currentSelectable != null)
            {
                currentSelectable.Select();
            }

            if (debugLogging)
                Debug.Log("[HierarchicalSettingsNavigation] Exited Setting Adjustment Level");
        }

        private void EnableCurrentSettingControls(bool enable)
        {
            var currentGameObject = EventSystem.current?.currentSelectedGameObject;
            if (currentGameObject == null) return;

            // Check for Settings Generator components and enable/disable their button controls
            var optionsButton = currentGameObject.GetComponent<OptionsButtonUGUI>();
            if (optionsButton != null)
            {
                optionsButton.EnableButtonControls = enable;
                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] {(enable ? "Enabled" : "Disabled")} OptionsButtonUGUI controls for {currentGameObject.name}");
                return;
            }

            var stepper = currentGameObject.GetComponent<StepperUGUI>();
            if (stepper != null)
            {
                stepper.EnableButtonControls = enable;
                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] {(enable ? "Enabled" : "Disabled")} StepperUGUI controls for {currentGameObject.name}");
                return;
            }

            var slider = currentGameObject.GetComponent<SliderUGUI>();
            if (slider != null)
            {
                slider.UseMoveCommandToChangeValue = enable;
                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] {(enable ? "Enabled" : "Disabled")} SliderUGUI controls for {currentGameObject.name}");
                return;
            }

            // Check for DropDownUGUI (which wraps TMP_Dropdown)
            var dropdownUGUI = currentGameObject.GetComponent<DropDownUGUI>();
            if (dropdownUGUI != null)
            {
                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] Found DropDownUGUI {currentGameObject.name} - using standard dropdown behavior");
                return;
            }

            // For regular TMP_Dropdown, we don't need to do anything special
            var dropdown = currentGameObject.GetComponent<TMPro.TMP_Dropdown>();
            if (dropdown != null)
            {
                if (debugLogging)
                    Debug.Log($"[HierarchicalSettingsNavigation] Found TMP_Dropdown {currentGameObject.name} - using default behavior");
                return;
            }

            if (debugLogging)
                Debug.Log($"[HierarchicalSettingsNavigation] No Settings Generator component found on {currentGameObject.name}");
        }

        #endregion

        #region Main Menu Integration

        public void ExitToMainMenu()
        {
            // This should be called by the main back button or cancel from category level
            var simpleMenUI = GetComponentInParent<SimpleMenUISystem>();
            if (simpleMenUI != null)
            {
                // Settings are now handled by Last UI, so just show pause menu
                simpleMenUI.ShowPause();
            }

            if (debugLogging)
                Debug.Log("[HierarchicalSettingsNavigation] Exited to Main Menu");
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Call this when entering the settings panel
        /// </summary>
        public void Initialize()
        {
            EnterCategorySelectionLevel();
        }

        /// <summary>
        /// Get current navigation level
        /// </summary>
        public string GetCurrentLevel()
        {
            return currentLevel.ToString();
        }

        /// <summary>
        /// Force enter setting adjustment mode (for testing)
        /// </summary>
        [ContextMenu("Test Enter Setting Adjustment")]
        public void TestEnterSettingAdjustment()
        {
            if (currentLevel == NavigationLevel.SettingsSelection)
            {
                EnterSettingAdjustment();
            }
            else
            {
                Debug.Log("Must be in SettingsSelection level to enter adjustment mode");
            }
        }

        /// <summary>
        /// Force exit setting adjustment mode (for testing)
        /// </summary>
        [ContextMenu("Test Exit Setting Adjustment")]
        public void TestExitSettingAdjustment()
        {
            if (currentLevel == NavigationLevel.SettingAdjustment)
            {
                ExitSettingAdjustment();
            }
            else
            {
                Debug.Log("Must be in SettingAdjustment level to exit adjustment mode");
            }
        }

        #endregion

        #region Properties

        public Button MainBackButton
        {
            get => mainBackButton;
            set => mainBackButton = value;
        }

        public Button FirstPauseMenuButton
        {
            get => firstPauseMenuButton;
            set => firstPauseMenuButton = value;
        }

        #endregion

        #region Debug Methods

        [ContextMenu("Debug Setup")]
        public void DebugSetup()
        {
            Debug.Log("=== HierarchicalSettingsNavigation Debug ===");
            Debug.Log($"Category Buttons: {(categoryButtons != null ? categoryButtons.Length : 0)}");
            Debug.Log($"Category Panels: {(categoryPanels != null ? categoryPanels.Length : 0)}");
            Debug.Log($"Current Level: {currentLevel}");
            Debug.Log($"Current Category Index: {currentCategoryIndex}");

            if (categoryButtons != null)
            {
                for (int i = 0; i < categoryButtons.Length; i++)
                {
                    Debug.Log($"Category Button {i}: {(categoryButtons[i] != null ? categoryButtons[i].name : "NULL")}");
                }
            }

            if (categoryPanels != null)
            {
                for (int i = 0; i < categoryPanels.Length; i++)
                {
                    if (categoryPanels[i] != null)
                    {
                        Debug.Log($"Category Panel {i}: {categoryPanels[i].name} - Active: {categoryPanels[i].activeInHierarchy}");
                        var selectables = categoryPanels[i].GetComponentsInChildren<Selectable>(true);
                        Debug.Log($"  Selectables found: {selectables.Length}");
                        foreach (var s in selectables)
                        {
                            Debug.Log($"    - {s.name} ({s.GetType().Name}) - Active: {s.gameObject.activeInHierarchy}, Interactable: {s.interactable}");
                        }
                    }
                    else
                    {
                        Debug.Log($"Category Panel {i}: NULL");
                    }
                }
            }
            Debug.Log("=== End Debug ===");
        }

        [ContextMenu("Test Enter Graphics")]
        public void TestEnterGraphics()
        {
            Debug.Log("Testing Enter Graphics...");
            EnterCategorySettings(0);
        }

        [ContextMenu("Test Enter Audio")]
        public void TestEnterAudio()
        {
            Debug.Log("Testing Enter Audio...");
            EnterCategorySettings(1);
        }

        [ContextMenu("Setup Button Events")]
        public void SetupButtonEvents()
        {
            Debug.Log("Setting up button click events...");

            // Clear existing events and set up new ones
            for (int i = 0; i < categoryButtons.Length; i++)
            {
                if (categoryButtons[i] != null)
                {
                    int index = i; // Capture for closure
                    categoryButtons[i].onClick.RemoveAllListeners();
                    categoryButtons[i].onClick.AddListener(() =>
                    {
                        Debug.Log($"Button {index} clicked!");
                        EnterCategorySettings(index);
                    });
                    Debug.Log($"Set up click event for button {i} ({categoryButtons[i].name})");
                }
            }

            if (mainBackButton != null)
            {
                mainBackButton.onClick.RemoveAllListeners();
                mainBackButton.onClick.AddListener(() =>
                {
                    Debug.Log("Main back button clicked!");
                    ExitToMainMenu();
                });
                Debug.Log("Set up click event for main back button");
            }
        }

        #endregion
    }
}
