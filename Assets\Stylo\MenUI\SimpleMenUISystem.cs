using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using UnityEngine.SceneManagement;
using TMPro;
using Stylo.MenUI.UI;
using System.Reflection;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;
#endif

namespace Stylo.MenUI
{
    /// <summary>
    /// Simple, streamlined pause menu system focused on core pause functionality.
    /// Settings are handled by Last UI system loaded as an additive scene.
    /// Clean separation: MenUI = pause/resume, Last UI = comprehensive settings.
    /// </summary>
    public class SimpleMenUISystem : MonoBehaviour
    {
        [Header("UI Panels")]
        [SerializeField] private GameObject pauseMenuPanel;
        [SerializeField] private GameObject overlayPanel;

        [Header("Pause Menu Buttons")]
        [SerializeField] private Button resumeButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private Button exitButton;

        [Header("Configuration")]
        [SerializeField] private bool enableDebugMode = true;
        [SerializeField] private bool pauseAudio = true;

        [Header("Last UI Integration")]
        [SerializeField] private SceneReference lastUISceneReference;
        [SerializeField] private bool hidePauseMenuWhenSettingsOpen = false;

        // Input system
        private DefaultControls _controls;
        private InputAction _pauseAction;

        // State
        private bool _isPaused = false;
        private float _previousTimeScale = 1f;

        // Components
        private LastUISceneBridge _sceneBridge;

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeInput();
            SetupButtons();
            SetupExplicitNavigation();
            InitializeSceneBridge();
        }

        private void Start()
        {
            // Hide all panels initially
            HideAllPanels();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Initialized as simple pause menu. Settings handled by Last UI.");
        }

        private void OnEnable()
        {
            if (_controls != null)
            {
                _controls.Enable();
                _pauseAction.performed += OnPauseInput;
            }
        }

        private void OnDisable()
        {
            if (_controls != null)
            {
                _pauseAction.performed -= OnPauseInput;
                _controls.Disable();
            }
        }

        private void OnDestroy()
        {
            _controls?.Dispose();
        }

        #endregion

        #region Initialization

        private void InitializeInput()
        {
            _controls = new DefaultControls();
            _pauseAction = _controls.UI.Pause;
        }

        private void SetupButtons()
        {
            // Pause menu buttons
            if (resumeButton != null)
                resumeButton.onClick.AddListener(OnResumeClicked);

            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);

            if (exitButton != null)
                exitButton.onClick.AddListener(OnExitClicked);

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Core pause menu buttons setup complete");
        }

        private void HideAllPanels()
        {
            if (pauseMenuPanel != null) pauseMenuPanel.SetActive(false);
            if (overlayPanel != null) overlayPanel.SetActive(false);
        }

        #endregion

        #region Input Handling

        private void OnPauseInput(InputAction.CallbackContext context)
        {
            TogglePause();
        }

        #endregion

        #region Button Callbacks

        private void OnResumeClicked()
        {
            HidePause();
        }

        private async void OnSettingsClicked()
        {
            if (enableDebugMode)
                Debug.Log($"SimpleMenUISystem: Settings button clicked. Current pause state: {_isPaused}, TimeScale: {Time.timeScale}");

            if (_sceneBridge != null)
            {
                bool success = await _sceneBridge.LoadLastUISettingsScene();
                if (!success)
                {
                    Debug.LogError("SimpleMenUISystem: Failed to load Last UI settings scene");
                    // Could show error message to user here
                }
                else if (enableDebugMode)
                {
                    Debug.Log($"SimpleMenUISystem: Last UI loaded successfully. Pause state: {_isPaused}, TimeScale: {Time.timeScale}");
                }
            }
            else
            {
                Debug.LogError("SimpleMenUISystem: Scene bridge not initialized");
            }
        }

        private void OnExitClicked()
        {
            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Exit button clicked");

#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }



        #endregion

        #region Public API

        public void TogglePause()
        {
            if (_isPaused)
                HidePause();
            else
                ShowPause();
        }

        public void ShowPause()
        {
            if (_isPaused) return;

            _isPaused = true;

            // Show overlay
            if (overlayPanel != null)
                overlayPanel.SetActive(true);

            // Show pause menu
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);

            // Pause systems
            PauseAllSystems();

            // Set focus
            if (resumeButton != null)
                resumeButton.Select();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Pause menu shown");
        }

        public void HidePause()
        {
            if (!_isPaused) return;

            _isPaused = false;

            // Hide all panels
            HideAllPanels();

            // Resume systems
            ResumeAllSystems();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Pause menu hidden and game resumed");
        }

        /// <summary>
        /// Hide the pause menu visually without unpausing the game
        /// Used when loading settings to keep game paused but hide menu
        /// </summary>
        public void HidePauseMenuOnly()
        {
            // Hide all panels but keep pause state
            HideAllPanels();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Pause menu hidden (game remains paused)");
        }

        /// <summary>
        /// Show the pause menu visually without changing pause state
        /// Used when returning from settings to restore menu visibility
        /// </summary>
        public void ShowPauseMenuOnly()
        {
            // Show pause menu panels without changing pause state
            if (overlayPanel != null)
                overlayPanel.SetActive(true);

            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);

            // Set focus
            if (resumeButton != null)
                resumeButton.Select();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Pause menu shown (pause state unchanged)");
        }

        /// <summary>
        /// Called by Last UI when settings are complete - unloads the scene and returns to pause menu
        /// </summary>
        public async System.Threading.Tasks.Task OnLastUISettingsComplete()
        {
            if (_sceneBridge != null)
            {
                bool success = await _sceneBridge.UnloadLastUIScene();
                if (success)
                {
                    // Return to pause menu
                    if (pauseMenuPanel != null)
                    {
                        pauseMenuPanel.SetActive(true);
                        if (settingsButton != null)
                            settingsButton.Select();
                    }

                    if (enableDebugMode)
                        Debug.Log("SimpleMenUISystem: Returned to pause menu from Last UI settings");
                }
                else
                {
                    Debug.LogError("SimpleMenUISystem: Failed to unload Last UI scene");
                }
            }
            else
            {
                Debug.LogError("SimpleMenUISystem: Scene bridge not initialized");
            }
        }











        /// <summary>
        /// Ensures a selectable has proper navigation setup
        /// </summary>
        /// <param name="selectable">The selectable to setup</param>
        private void EnsureSelectableNavigation(Selectable selectable)
        {
            var nav = selectable.navigation;
            if (nav.mode == Navigation.Mode.None || nav.mode == Navigation.Mode.Automatic)
            {
                nav.mode = Navigation.Mode.Explicit;
                selectable.navigation = nav;
            }
        }

        /// <summary>
        /// Sets up vertical navigation chain for a list of selectables
        /// </summary>
        /// <param name="selectables">List of selectables to chain together</param>
        private void SetupVerticalNavigationChain(List<Selectable> selectables)
        {
            for (int i = 0; i < selectables.Count; i++)
            {
                var nav = selectables[i].navigation;
                nav.mode = Navigation.Mode.Explicit;

                // Set up/down navigation
                if (i > 0)
                    nav.selectOnUp = selectables[i - 1];

                if (i < selectables.Count - 1)
                    nav.selectOnDown = selectables[i + 1];

                selectables[i].navigation = nav;
            }

            if (enableDebugMode && selectables.Count > 0)
                Debug.Log($"SimpleMenUISystem: Setup navigation chain for {selectables.Count} selectables");
        }

        #endregion

        #region Controller Navigation Helpers





        /// <summary>
        /// Sets up explicit navigation between UI elements for better controller support
        /// </summary>
        private void SetupExplicitNavigation()
        {
            // Setup navigation for core pause menu buttons only
            SetupButtonNavigation(new[] { resumeButton, settingsButton, exitButton });

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Core pause menu navigation setup complete");
        }

        /// <summary>
        /// Sets up navigation between an array of buttons in vertical order
        /// </summary>
        /// <param name="buttons">Array of buttons to setup navigation for</param>
        private void SetupButtonNavigation(Button[] buttons)
        {
            if (buttons == null || buttons.Length == 0) return;

            for (int i = 0; i < buttons.Length; i++)
            {
                if (buttons[i] == null) continue;

                var nav = buttons[i].navigation;
                nav.mode = Navigation.Mode.Explicit;

                // Set up navigation
                if (i > 0 && buttons[i - 1] != null)
                    nav.selectOnUp = buttons[i - 1];

                if (i < buttons.Length - 1 && buttons[i + 1] != null)
                    nav.selectOnDown = buttons[i + 1];

                buttons[i].navigation = nav;
            }
        }





        #endregion

        #region System Control

        private void PauseAllSystems()
        {
            // Store current time scale and pause Unity
            _previousTimeScale = Time.timeScale;
            Time.timeScale = 0f;

            // Pause audio
            if (pauseAudio)
                AudioListener.pause = true;

            // Pause Epoch time system
            if (TimeManager.Instance != null)
                TimeManager.Instance.PauseTime();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: All systems paused");
        }

        private void ResumeAllSystems()
        {
            // Restore time scale
            Time.timeScale = _previousTimeScale;

            // Resume audio
            if (pauseAudio)
                AudioListener.pause = false;

            // Resume Epoch time system
            if (TimeManager.Instance != null)
                TimeManager.Instance.ResumeTime();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: All systems resumed");
        }

        #endregion

        #region Last UI Integration

        /// <summary>
        /// Initialize the scene bridge component
        /// </summary>
        private void InitializeSceneBridge()
        {
            _sceneBridge = GetComponent<LastUISceneBridge>();
            if (_sceneBridge == null)
            {
                // Add the bridge component if it doesn't exist
                _sceneBridge = gameObject.AddComponent<LastUISceneBridge>();

                if (enableDebugMode)
                    Debug.Log("SimpleMenUISystem: Added LastUISceneBridge component");
            }

            // Configure the bridge with our settings
            ConfigureSceneBridge();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Scene bridge initialized");
        }

        /// <summary>
        /// Configure the scene bridge with current settings
        /// </summary>
        private void ConfigureSceneBridge()
        {
            if (_sceneBridge != null)
            {
                string sceneName = GetSceneNameFromAsset();

                // Use reflection to set private fields since they're SerializeField
                var sceneNameField = typeof(LastUISceneBridge).GetField("lastUISceneName",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var hidePauseField = typeof(LastUISceneBridge).GetField("hidePauseMenuWhenSettingsOpen",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (sceneNameField != null)
                    sceneNameField.SetValue(_sceneBridge, sceneName);

                if (hidePauseField != null)
                    hidePauseField.SetValue(_sceneBridge, hidePauseMenuWhenSettingsOpen);

                if (enableDebugMode)
                    Debug.Log($"SimpleMenUISystem: Configured bridge - Scene: {sceneName}, HidePause: {hidePauseMenuWhenSettingsOpen}");
            }
        }

        /// <summary>
        /// Get scene name from the assigned SceneReference
        /// </summary>
        private string GetSceneNameFromAsset()
        {
            if (lastUISceneReference == null)
            {
                Debug.LogWarning("[SimpleMenUISystem] No Last UI scene reference assigned! Please assign a SceneReference in the inspector.");
                return "demo"; // Fallback
            }

            if (!lastUISceneReference.IsValid)
            {
                Debug.LogWarning($"[SimpleMenUISystem] Scene reference '{lastUISceneReference.name}' is invalid. Check that the scene is assigned and in build settings.");
                return lastUISceneReference.SceneName ?? "demo"; // Try to use the name anyway
            }

            return lastUISceneReference.SceneName;
        }



        /// <summary>
        /// Check if Last UI settings scene is currently loaded
        /// </summary>
        public bool IsLastUISceneLoaded => _sceneBridge?.IsLastUISceneLoaded ?? false;

        /// <summary>
        /// Get the configured Last UI scene name
        /// </summary>
        public string GetLastUISceneName() => _sceneBridge?.GetLastUISceneName() ?? "LastUI_Settings";

        /// <summary>
        /// Get reference to the scene bridge component
        /// </summary>
        public LastUISceneBridge GetSceneBridge() => _sceneBridge;

        /// <summary>
        /// Check if the game is currently paused
        /// </summary>
        public bool IsPaused => _isPaused;

        /// <summary>
        /// Get current time scale for debugging
        /// </summary>
        public float CurrentTimeScale => Time.timeScale;

        #endregion
    }
}


