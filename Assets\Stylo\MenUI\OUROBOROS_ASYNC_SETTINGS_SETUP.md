# Ouroboros + Async Settings MenUI Setup Guide

## 🎯 Current Setup

**Main Scene**: `Assets/_Scenes/Levels/Ouroboros - Base.unity`  
**Settings Scene**: `Assets/Settings Menu/Last UI/Scenes/Async Settings.unity`

## ✅ What's Already Configured

### **Ouroboros - Base Scene**
- ✅ **MenUI Canvas** - Already present in scene
- ✅ **SimpleMenUISystem** - Component exists with SceneReference field
- ✅ **GameManager** - Has `pauseMenuManager` field ready for assignment

### **Async Settings Scene**
- ✅ **StateManager** - Last UI state management system present
- ✅ **Canvas** - UI canvas for settings interface
- ✅ **Last UI Components** - Full settings interface implemented

## 🔧 Setup Steps Required

### **1. Create SceneReference Asset**

1. **Create the asset**:
   - Right-click in Project → Create → Stylo → MenUI → Scene Reference
   - Name: "Async Settings Scene Reference"
   - Location: `Assets/Stylo/MenUI/Scene References/`

2. **Configure the asset**:
   - **Scene Asset**: Drag `Assets/Settings Menu/Last UI/Scenes/Async Settings.unity`
   - **Auto Update Name**: Keep checked (will show "Async Settings")

3. **Verify build settings**:
   - The asset will show if scene is in build settings
   - If not, use "Add to Build Settings" context menu

### **2. Configure Ouroboros - Base Scene**

1. **Open** `Assets/_Scenes/Levels/Ouroboros - Base.unity`

2. **Find SimpleMenUISystem**:
   - Look for "MenUI Canvas" GameObject
   - Select the GameObject with `SimpleMenUISystem` component

3. **Configure SimpleMenUISystem**:
   - **Last UI Scene Reference**: Drag the "Async Settings Scene Reference" asset
   - **Hide Pause Menu When Settings Open**: `false` (keep pause menu visible)
   - **Enable Debug Mode**: `true` (for testing)

4. **Assign to GameManager** (if needed):
   - Find GameManager GameObject
   - **Pause Menu Manager**: Drag the MenUI Canvas GameObject

### **3. Setup Async Settings Scene**

1. **Open** `Assets/Settings Menu/Last UI/Scenes/Async Settings.unity`

2. **Add required components**:
   - Create empty GameObject named "MenUI Integration"
   - Add `LastUIMenUIBridge` component
   - Add `DemoSceneAutoSetup` component (if not present)

3. **Configure LastUIMenUIBridge**:
   - **Enable Debug Logging**: `true`
   - **Listen For Escape Key**: `true`
   - **Listen For Cancel Input**: `true`
   - **Exit On First Canvas Only**: `true`

4. **Verify SceneServiceLocator**:
   - Check if `SceneServiceLocator` component exists
   - If not, the `DemoSceneAutoSetup` will create it

## 🎮 Expected Behavior

### **User Flow**
```
Ouroboros Game Running
     ↓ (Escape Key)
MenUI Pause Menu Appears (Game Paused)
     ↓ (Settings Button)
Async Settings Scene Loads Over Pause Menu (Game Stays Paused)
     ↓ (User Changes Settings)
Settings Auto-Save to ScriptableObject
     ↓ (Escape Key)
Async Settings Scene Unloads (Game Still Paused)
     ↓ (Pause Menu Still Visible)
User Can Resume or Exit
```

### **Technical Flow**
```
SimpleMenUISystem.OnSettingsClicked()
     ↓
LastUISceneBridge.LoadLastUISettingsScene()
     ↓
SceneManager.LoadSceneAsync("Async Settings", LoadSceneMode.Additive)
     ↓
DemoSceneAutoSetup.Start() (in Async Settings scene)
     ↓
LastUIMenUIBridge.Start() (sets up exit handling)
     ↓
User interacts with settings
     ↓
LastUIMenUIBridge.OnCancelInput() (Escape pressed)
     ↓
LastUISceneBridge.OnLastUIExitRequested()
     ↓
SceneManager.UnloadSceneAsync("Async Settings")
```

## 🐛 Troubleshooting

### **"Scene couldn't be loaded"**
- ✅ Check scene is in Build Settings
- ✅ Verify scene name matches exactly: "Async Settings"
- ✅ Check SceneReference asset is properly configured

### **Settings don't persist**
- ✅ Verify `DemoSceneAutoSetup` component exists in Async Settings scene
- ✅ Check for `LastUISettingsManager` creation in console
- ✅ Look for `SwitchHandlerAutoConfig` messages

### **Can't exit settings**
- ✅ Verify `LastUIMenUIBridge` component exists in Async Settings scene
- ✅ Check Input System is properly configured
- ✅ Look for "Cancel input detected" debug messages

### **Game unpauses when entering settings**
- ✅ Check `hidePauseMenuWhenSettingsOpen = false` in SimpleMenUISystem
- ✅ Verify pause state debug messages in console
- ✅ Use `PauseStateDebugger` component for monitoring

## 📁 File Structure

```
Assets/
├── _Scenes/Levels/Ouroboros - Base.unity     ✅ Main game scene
├── Settings Menu/Last UI/Scenes/
│   └── Async Settings.unity                  ✅ Settings scene
└── Stylo/MenUI/
    ├── Scene References/
    │   └── Async Settings Scene Reference.asset  📝 Create this
    ├── SimpleMenUISystem.cs                   ✅ Main pause controller
    ├── LastUISceneBridge.cs                   ✅ Async scene loader
    └── Documentation/                         ✅ Setup guides
```

## 🎉 Verification Checklist

### **Before Testing**
- [ ] SceneReference asset created and configured
- [ ] Async Settings scene in Build Settings
- [ ] SimpleMenUISystem configured with SceneReference
- [ ] LastUIMenUIBridge added to Async Settings scene
- [ ] DemoSceneAutoSetup added to Async Settings scene

### **During Testing**
- [ ] Escape opens pause menu in Ouroboros scene
- [ ] Settings button loads Async Settings scene
- [ ] Game remains paused throughout settings interaction
- [ ] Settings changes are saved and persist
- [ ] Escape exits settings and returns to pause menu
- [ ] Resume button unpauses game properly

### **Console Messages to Look For**
```
[SimpleMenUISystem] Settings button clicked. Current pause state: True, TimeScale: 0
[LastUISceneBridge] Loading Last UI scene: Async Settings
[DemoSceneAutoSetup] Setting up demo scene
[LastUIMenUIBridge] Initialized in Last UI scene
[LastUISceneBridge] Last UI scene loaded successfully
[LastUIMenUIBridge] Cancel input detected
[LastUISceneBridge] Last UI exit requested, unloading scene
[LastUISceneBridge] Last UI scene unloaded successfully
```

## 🚀 Ready to Test!

Once setup is complete, the integration should provide seamless pause menu → settings → pause menu flow with proper pause state management and settings persistence.
