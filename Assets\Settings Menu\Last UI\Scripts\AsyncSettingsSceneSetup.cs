using UnityEngine;

/// <summary>
/// Setup script for Async Settings scene to ensure MenUI integration components are present.
/// Add this component to any GameObject in the Async Settings scene to auto-setup MenUI integration.
/// </summary>
public class AsyncSettingsSceneSetup : MonoBehaviour
{
    [Header("Setup Configuration")]
    [SerializeField] private bool enableDebugLogging = true;
    [SerializeField] private bool autoSetupOnStart = true;

    [Header("Status (Read-Only)")]
    [SerializeField] private bool setupComplete = false;
    [SerializeField] private bool sceneServiceLocatorFound = false;
    [SerializeField] private bool lastUISettingsManagerFound = false;
    [SerializeField] private bool switchHandlerAutoConfigFound = false;
    [SerializeField] private bool lastUIMenUIBridgeFound = false;

    private void Start()
    {
        if (autoSetupOnStart)
        {
            SetupAsyncSettingsScene();
        }
    }

    /// <summary>
    /// Setup all required components for MenUI integration
    /// </summary>
    [ContextMenu("Setup Async Settings Scene")]
    public void SetupAsyncSettingsScene()
    {
        if (enableDebugLogging)
            Debug.Log("[AsyncSettingsSceneSetup] Setting up Async Settings scene for MenUI integration...");

        CheckExistingComponents();
        CreateMissingComponents();
        
        setupComplete = true;
        
        if (enableDebugLogging)
            Debug.Log("[AsyncSettingsSceneSetup] Async Settings scene setup complete!");
    }

    /// <summary>
    /// Check which components already exist in the scene
    /// </summary>
    private void CheckExistingComponents()
    {
        sceneServiceLocatorFound = FindObjectOfType<SceneServiceLocator>() != null;
        lastUISettingsManagerFound = FindObjectOfType<LastUISettingsManager>() != null;
        switchHandlerAutoConfigFound = FindObjectOfType<SwitchHandlerAutoConfig>() != null;
        lastUIMenUIBridgeFound = FindObjectOfType<LastUIMenUIBridge>() != null;

        if (enableDebugLogging)
        {
            Debug.Log($"[AsyncSettingsSceneSetup] Existing components check:");
            Debug.Log($"  - SceneServiceLocator: {sceneServiceLocatorFound}");
            Debug.Log($"  - LastUISettingsManager: {lastUISettingsManagerFound}");
            Debug.Log($"  - SwitchHandlerAutoConfig: {switchHandlerAutoConfigFound}");
            Debug.Log($"  - LastUIMenUIBridge: {lastUIMenUIBridgeFound}");
        }
    }

    /// <summary>
    /// Create any missing components required for MenUI integration
    /// </summary>
    private void CreateMissingComponents()
    {
        // Create SceneServiceLocator if missing
        if (!sceneServiceLocatorFound)
        {
            var serviceLocatorGO = new GameObject("SceneServiceLocator");
            serviceLocatorGO.AddComponent<SceneServiceLocator>();
            sceneServiceLocatorFound = true;
            
            if (enableDebugLogging)
                Debug.Log("[AsyncSettingsSceneSetup] Created SceneServiceLocator");
        }

        // Create LastUISettingsManager if missing
        if (!lastUISettingsManagerFound)
        {
            var settingsManagerGO = new GameObject("LastUISettingsManager");
            settingsManagerGO.AddComponent<LastUISettingsManager>();
            lastUISettingsManagerFound = true;
            
            if (enableDebugLogging)
                Debug.Log("[AsyncSettingsSceneSetup] Created LastUISettingsManager");
        }

        // Create SwitchHandlerAutoConfig if missing
        if (!switchHandlerAutoConfigFound)
        {
            var autoConfigGO = new GameObject("SwitchHandlerAutoConfig");
            autoConfigGO.AddComponent<SwitchHandlerAutoConfig>();
            switchHandlerAutoConfigFound = true;
            
            if (enableDebugLogging)
                Debug.Log("[AsyncSettingsSceneSetup] Created SwitchHandlerAutoConfig");
        }

        // Create LastUIMenUIBridge if missing
        if (!lastUIMenUIBridgeFound)
        {
            var menUIBridgeGO = new GameObject("LastUIMenUIBridge");
            menUIBridgeGO.AddComponent<LastUIMenUIBridge>();
            lastUIMenUIBridgeFound = true;
            
            if (enableDebugLogging)
                Debug.Log("[AsyncSettingsSceneSetup] Created LastUIMenUIBridge");
        }
    }

    /// <summary>
    /// Validate that all required components are present and working
    /// </summary>
    [ContextMenu("Validate Setup")]
    public void ValidateSetup()
    {
        CheckExistingComponents();
        
        bool allComponentsPresent = sceneServiceLocatorFound && 
                                   lastUISettingsManagerFound && 
                                   switchHandlerAutoConfigFound && 
                                   lastUIMenUIBridgeFound;

        if (allComponentsPresent)
        {
            Debug.Log("[AsyncSettingsSceneSetup] ✅ All required components are present!");
            
            // Additional validation
            var bridge = FindObjectOfType<LastUIMenUIBridge>();
            if (bridge != null && bridge.IsMenUIBridgeAvailable)
            {
                Debug.Log("[AsyncSettingsSceneSetup] ✅ MenUI bridge connection is available!");
            }
            else
            {
                Debug.LogWarning("[AsyncSettingsSceneSetup] ⚠️ MenUI bridge connection not available (this is normal if main scene isn't loaded)");
            }
        }
        else
        {
            Debug.LogError("[AsyncSettingsSceneSetup] ❌ Some required components are missing!");
            Debug.LogError($"Missing: " +
                          $"{(sceneServiceLocatorFound ? "" : "SceneServiceLocator ")} " +
                          $"{(lastUISettingsManagerFound ? "" : "LastUISettingsManager ")} " +
                          $"{(switchHandlerAutoConfigFound ? "" : "SwitchHandlerAutoConfig ")} " +
                          $"{(lastUIMenUIBridgeFound ? "" : "LastUIMenUIBridge ")}");
        }
    }

    /// <summary>
    /// Force refresh component status
    /// </summary>
    [ContextMenu("Refresh Status")]
    public void RefreshStatus()
    {
        CheckExistingComponents();
        
        if (enableDebugLogging)
            Debug.Log("[AsyncSettingsSceneSetup] Component status refreshed");
    }

    #if UNITY_EDITOR
    private void OnValidate()
    {
        // Update status when inspector values change
        if (Application.isPlaying)
        {
            CheckExistingComponents();
        }
    }
    #endif
}

#if UNITY_EDITOR
using UnityEditor;

[CustomEditor(typeof(AsyncSettingsSceneSetup))]
public class AsyncSettingsSceneSetupEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();
        
        EditorGUILayout.Space();
        
        AsyncSettingsSceneSetup setup = (AsyncSettingsSceneSetup)target;
        
        EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
        
        if (GUILayout.Button("Setup Async Settings Scene"))
        {
            setup.SetupAsyncSettingsScene();
        }
        
        if (GUILayout.Button("Validate Setup"))
        {
            setup.ValidateSetup();
        }
        
        if (GUILayout.Button("Refresh Status"))
        {
            setup.RefreshStatus();
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox(
            "This component ensures all required components for MenUI integration are present in the Async Settings scene. " +
            "Click 'Setup Async Settings Scene' to automatically create missing components.",
            MessageType.Info);
    }
}
#endif
