using UnityEngine;
using UnityEngine.InputSystem;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Stylo.MenUI.Debug
{
    /// <summary>
    /// Debug utility to monitor pause state in real-time.
    /// Attach to any GameObject to see pause state in inspector and console.
    /// </summary>
    public class PauseStateDebugger : MonoBehaviour
    {
        [Header("Debug Configuration")]
        [SerializeField] private bool enableConsoleLogging = true;
        [SerializeField] private bool enableInspectorDisplay = true;
        [SerializeField] private float logInterval = 1f;

        [Header("Current State (Read-Only)")]
        [SerializeField] private bool isPaused;
        [SerializeField] private float timeScale;
        [SerializeField] private bool audioListenerPaused;
        [SerializeField] private bool menUISystemFound;
        [SerializeField] private bool menUIIsPaused;

        private SimpleMenUISystem _menUISystem;
        private float _lastLogTime;

        private void Start()
        {
            _menUISystem = FindObjectOfType<SimpleMenUISystem>();

            if (_menUISystem == null)
            {
                Debug.LogWarning("[PauseStateDebugger] No SimpleMenUISystem found in scene");
            }
            else
            {
                Debug.Log("[PauseStateDebugger] Found SimpleMenUISystem, monitoring pause state");
            }
        }

        private void Update()
        {
            UpdateState();

            if (enableConsoleLogging && Time.unscaledTime - _lastLogTime >= logInterval)
            {
                LogCurrentState();
                _lastLogTime = Time.unscaledTime;
            }
        }

        private void UpdateState()
        {
            isPaused = Time.timeScale == 0f;
            timeScale = Time.timeScale;
            audioListenerPaused = AudioListener.pause;
            menUISystemFound = _menUISystem != null;
            menUIIsPaused = _menUISystem?.IsPaused ?? false;
        }

        private void LogCurrentState()
        {
            string state = $"[PauseStateDebugger] " +
                          $"TimeScale: {timeScale:F2}, " +
                          $"AudioPaused: {audioListenerPaused}, " +
                          $"MenUI Found: {menUISystemFound}, " +
                          $"MenUI Paused: {menUIIsPaused}";

            if (isPaused != menUIIsPaused && menUISystemFound)
            {
                Debug.LogWarning($"{state} ⚠️ MISMATCH: TimeScale suggests paused={isPaused} but MenUI says paused={menUIIsPaused}");
            }
            else
            {
                Debug.Log(state);
            }
        }

        /// <summary>
        /// Force log current state (useful for debugging specific moments)
        /// </summary>
        [ContextMenu("Log Current State")]
        public void ForceLogState()
        {
            UpdateState();
            LogCurrentState();
        }

        /// <summary>
        /// Test pause/unpause functionality
        /// </summary>
        [ContextMenu("Test Pause Toggle")]
        public void TestPauseToggle()
        {
            if (_menUISystem != null)
            {
                Debug.Log("[PauseStateDebugger] Testing pause toggle...");
                _menUISystem.TogglePause();
                ForceLogState();
            }
            else
            {
                Debug.LogWarning("[PauseStateDebugger] No MenUI system to test");
            }
        }

        /// <summary>
        /// Test settings loading
        /// </summary>
        [ContextMenu("Test Settings Load")]
        public void TestSettingsLoad()
        {
            if (_menUISystem != null)
            {
                Debug.Log("[PauseStateDebugger] Testing settings load...");

                // First ensure we're paused
                if (!_menUISystem.IsPaused)
                {
                    _menUISystem.ShowPause();
                }

                ForceLogState();

                // Try to load settings
                var bridge = _menUISystem.GetSceneBridge();
                if (bridge != null)
                {
                    Debug.Log("[PauseStateDebugger] Loading Last UI scene...");
                    // Note: This is async, so we can't immediately check the result
                }
                else
                {
                    Debug.LogWarning("[PauseStateDebugger] No scene bridge found");
                }
            }
            else
            {
                Debug.LogWarning("[PauseStateDebugger] No MenUI system to test");
            }
        }

        private void OnGUI()
        {
            if (!enableInspectorDisplay) return;

            GUILayout.BeginArea(new Rect(10, 10, 300, 150));
            GUILayout.BeginVertical("box");

            GUILayout.Label("Pause State Debug", EditorGUIUtility.isProSkin ? GUI.skin.label : GUI.skin.box);

            GUILayout.Label($"Time Scale: {timeScale:F2}");
            GUILayout.Label($"Audio Paused: {audioListenerPaused}");
            GUILayout.Label($"MenUI Found: {menUISystemFound}");
            GUILayout.Label($"MenUI Paused: {menUIIsPaused}");

            if (isPaused != menUIIsPaused && menUISystemFound)
            {
                GUI.color = Color.red;
                GUILayout.Label("⚠️ STATE MISMATCH!");
                GUI.color = Color.white;
            }
            else
            {
                GUI.color = Color.green;
                GUILayout.Label("✅ States Match");
                GUI.color = Color.white;
            }

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }

#if UNITY_EDITOR
    [CustomEditor(typeof(PauseStateDebugger))]
    public class PauseStateDebuggerEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();

            EditorGUILayout.Space();

            PauseStateDebugger debugger = (PauseStateDebugger)target;

            if (GUILayout.Button("Force Log State"))
            {
                debugger.ForceLogState();
            }

            if (GUILayout.Button("Test Pause Toggle"))
            {
                debugger.TestPauseToggle();
            }

            if (GUILayout.Button("Test Settings Load"))
            {
                debugger.TestSettingsLoad();
            }
        }
    }
#endif
}
